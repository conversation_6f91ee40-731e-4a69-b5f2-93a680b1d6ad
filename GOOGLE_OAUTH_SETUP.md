# Google OAuth Setup Guide

## Current Configuration Status

### Environment Variables (✅ Configured)

- `NEXT_PUBLIC_COGNITO_USER_POOL_ID`: us-east-2_bHQV6YVnt
- `NEXT_PUBLIC_COGNITO_CLIENT_ID`: 3aju3epc50421b4fp0bh75oj4u
- `NEXT_PUBLIC_COGNITO_DOMAIN`: us-east-2bhqv6yvnt.auth.us-east-2.amazoncognito.com

### Required AWS Cognito Configuration

#### 1. User Pool App Client Settings

Navigate to AWS Cognito → User Pools → us-east-2_bHQV6YVnt → App integration → App clients

**Callback URLs** (must include):

```
http://localhost:3000/oauth-callback
https://your-production-domain.com/oauth-callback
```

**Sign out URLs** (must include):

```
http://localhost:3000/signin
https://your-production-domain.com/signin
```

**Identity providers** (must be enabled):

- ✅ Google

**OAuth grant types** (must be enabled):

- ✅ Authorization code grant

**OAuth scopes** (must be enabled):

- ✅ email
- ✅ openid
- ✅ profile

#### 2. Hosted UI Domain

Navigate to AWS Cognito → User Pools → us-east-2_bHQV6YVnt → App integration → Domain

**Domain name**: us-east-2bhqv6yvnt.auth.us-east-2.amazoncognito.com

- ✅ Should be active and available

#### 3. Google Identity Provider

Navigate to AWS Cognito → User Pools → us-east-2_bHQV6YVnt → Sign-in experience → Federated identity provider sign-in

**Google provider** must be configured with:

- Client ID from Google Cloud Console
- Client secret from Google Cloud Console
- Authorized scopes: email, openid, profile

### Required Google Cloud Console Configuration

#### 1. OAuth 2.0 Client IDs

Navigate to Google Cloud Console → APIs & Services → Credentials

**Authorized JavaScript origins**:

```
http://localhost:3000
https://your-production-domain.com
```

**Authorized redirect URIs**:

```
https://us-east-2bhqv6yvnt.auth.us-east-2.amazoncognito.com/oauth2/idpresponse
```

## Testing Steps

1. **Test Configuration Page**: Visit `/test-google-auth` to verify environment variables
2. **Test Google Button**: Click the Google sign-in button on `/signin`
3. **Check Console**: Look for detailed error messages in browser console
4. **Verify Redirect**: Ensure redirect goes to Google OAuth, then back to `/oauth-callback`

## Common Issues & Solutions

### Issue: "oauth param not configured"

**Solution**: Check that OAuth scopes are enabled in Cognito app client

### Issue: "Invalid domain"

**Solution**: Verify Cognito domain is active and matches environment variable

### Issue: "redirect_uri_mismatch"

**Solution**: Add callback URL to both Cognito and Google Cloud Console

### Issue: Google OAuth works but callback fails

**Solution**: Check `/oauth-callback` route and `handleOAuthCallback` function

## Current Implementation Files

- **Button Component**: `components/auth/google-sign-in-button.tsx`
- **Amplify Config**: `lib/amplify-config.ts`
- **OAuth Callback**: `app/(auth)/oauth-callback/page.tsx`
- **Auth Actions**: `app/(auth)/actions.ts`
- **Environment**: `.env.local`

## Next Steps

1. Verify all AWS Cognito settings match this guide
2. Test Google sign-in flow end-to-end
3. Check browser console for any remaining errors
4. Ensure Google Cloud Console redirect URIs are correct
