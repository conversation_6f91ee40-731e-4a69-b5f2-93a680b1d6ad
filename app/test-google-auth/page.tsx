'use client';

import { useEffect, useState } from 'react';
import { configureAmplify } from '@/lib/amplify-config';
import { signInWithRedirect } from 'aws-amplify/auth';
// import { GoogleSignInButton } from '@/components/auth/google-sign-in-button';
// import { AuthProvider } from '@/components/auth/auth-provider';

export default function TestGoogleAuthPage() {
  const [config, setConfig] = useState<any>(null);
  const [error, setError] = useState<string>('');
  const [testResult, setTestResult] = useState<string>('');

  useEffect(() => {
    try {
      configureAmplify();

      // Check environment variables
      const envConfig = {
        userPoolId: process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID,
        clientId: process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID,
        domain: process.env.NEXT_PUBLIC_COGNITO_DOMAIN,
        currentOrigin: typeof window !== 'undefined' ? window.location.origin : 'unknown',
      };

      setConfig(envConfig);

      // Validate configuration
      if (!envConfig.userPoolId) {
        setError('Missing NEXT_PUBLIC_COGNITO_USER_POOL_ID');
      } else if (!envConfig.clientId) {
        setError('Missing NEXT_PUBLIC_COGNITO_CLIENT_ID');
      } else if (!envConfig.domain) {
        setError('Missing NEXT_PUBLIC_COGNITO_DOMAIN');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Configuration error');
    }
  }, []);

  const testGoogleSignIn = async () => {
    try {
      setTestResult('Testing Google sign-in...');
      console.log('🧪 [TEST] Starting Google OAuth test...');

      await signInWithRedirect({
        provider: 'Google',
        customState: JSON.stringify({
          returnUrl: '/test-google-auth',
        }),
      });
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      setTestResult(`❌ Error: ${errorMsg}`);
      console.error('🧪 [TEST] Google OAuth test failed:', err);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Google Auth Configuration Test</h1>

        {error && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-6 mb-6">
            <h2 className="text-xl font-semibold text-red-400 mb-2">Configuration Error</h2>
            <p className="text-red-300">{error}</p>
          </div>
        )}

        {config && (
          <div className="bg-slate-800/50 border border-slate-600/50 rounded-xl p-6 mb-6">
            <h2 className="text-xl font-semibold text-white mb-4">Current Configuration</h2>
            <div className="space-y-3">
              <div>
                <span className="text-slate-400">User Pool ID:</span>
                <span className="text-white ml-2 font-mono">{config.userPoolId || 'Not set'}</span>
              </div>
              <div>
                <span className="text-slate-400">Client ID:</span>
                <span className="text-white ml-2 font-mono">{config.clientId || 'Not set'}</span>
              </div>
              <div>
                <span className="text-slate-400">Domain:</span>
                <span className="text-white ml-2 font-mono">{config.domain || 'Not set'}</span>
              </div>
              <div>
                <span className="text-slate-400">Current Origin:</span>
                <span className="text-white ml-2 font-mono">{config.currentOrigin}</span>
              </div>
            </div>
          </div>
        )}

        <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-6 mb-6">
          <h2 className="text-xl font-semibold text-blue-400 mb-4">
            Required Cognito Configuration
          </h2>
          <div className="space-y-2 text-sm">
            <p className="text-slate-300">
              <strong>Callback URLs:</strong>{' '}
              {typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3001'}
              /oauth-callback
            </p>
            <p className="text-slate-300">
              <strong>Sign out URLs:</strong>{' '}
              {typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3001'}
              /signin
            </p>
            <p className="text-slate-300">
              <strong>Identity providers:</strong> Google
            </p>
            <p className="text-slate-300">
              <strong>OAuth grant types:</strong> Authorization code grant
            </p>
            <p className="text-slate-300">
              <strong>OAuth scopes:</strong> email, openid, profile
            </p>
          </div>
        </div>

        <div className="bg-purple-500/10 border border-purple-500/20 rounded-xl p-6">
          <h2 className="text-xl font-semibold text-purple-400 mb-4">Test Google Sign-In</h2>
          <div className="space-y-4">
            <button
              onClick={testGoogleSignIn}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              Test Direct Google OAuth
            </button>

            <div className="border-t border-slate-600/50 pt-4">
              <p className="text-slate-400 text-sm mb-3">
                Go to the main sign-in page to test the Google button component:
              </p>
              <a
                href="/signin"
                className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Go to Sign-In Page
              </a>
            </div>

            {testResult && (
              <div className="mt-4 p-3 bg-slate-700/50 rounded-lg">
                <p className="text-slate-300 text-sm">{testResult}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
