import { Amplify } from 'aws-amplify';
import type { ResourcesConfig } from 'aws-amplify';

const createAmplifyConfig = (): ResourcesConfig => {
  const config: ResourcesConfig = {
    Auth: {
      Cognito: {
        userPoolId: process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID!,
        userPoolClientId: process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID!,
        signUpVerificationMethod: 'code',
        loginWith: {
          email: true,
          username: false,
          phone: false,
          oauth: {
            domain: process.env.NEXT_PUBLIC_COGNITO_DOMAIN!,
            scopes: ['email', 'profile', 'openid'],
            redirectSignIn: [
              typeof window !== 'undefined' ? `${window.location.origin}/oauth-callback` : 'http://localhost:3000/oauth-callback',
            ],
            redirectSignOut: [
              typeof window !== 'undefined' ? `${window.location.origin}/signin` : 'http://localhost:3000/signin',
            ],
            responseType: 'code',
            providers: ['Google'],
          },
        },
      },
    },
  };

  // Add identity pool if provided
  if (process.env.NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID) {
    config.Auth!.Cognito!.identityPoolId = process.env.NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID;
  }

  return config;
};

export const amplifyConfig = createAmplifyConfig();

let isConfigured = false;

export const configureAmplify = () => {
  if (!isConfigured && typeof window !== 'undefined') {
    try {
      Amplify.configure(amplifyConfig, { ssr: true });
      isConfigured = true;
    } catch (error) {
      console.warn('Failed to configure Amplify:', error);
    }
  }
};

// Auto-configure for client-side
if (typeof window !== 'undefined') {
  configureAmplify();
}
